#ifndef __BSP_SYSTEM_H_
#define __BSP_SYSTEM_H_

#include "main.h"
#include "dma.h"
#include "usart.h"
#include "gpio.h"

#include "string.h"
#include "stdio.h"
#include "math.h"
#include "stdarg.h"
#include "stdbool.h"

#include "uart.h"
#include "ringbuffer.h"

/* USART2和UART4环形缓冲区相关变量 */
extern struct rt_ringbuffer uart2_ring;     // USART2环形缓冲区
extern uint8_t uart2_rx_buf[64];            // USART2接收缓冲区
extern uint8_t ringbuffer_pool_2[64];       // USART2环形缓冲区内存池
extern uint8_t uart2_out[64];               // USART2输出缓冲区

extern struct rt_ringbuffer uart4_ring;     // UART4环形缓冲区
extern uint8_t uart4_rx_buf[64];            // UART4接收缓冲区
extern uint8_t ringbuffer_pool_4[64];       // UART4环形缓冲区内存池
extern uint8_t uart4_out[64];               // UART4输出缓冲区
#endif