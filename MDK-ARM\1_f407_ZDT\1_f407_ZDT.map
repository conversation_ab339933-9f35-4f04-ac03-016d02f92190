Component: Arm Compiler for Embedded 6.22 Tool: armlink [5ee90200]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.DMA1_Stream2_IRQHandler) for DMA1_Stream2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler) for DMA1_Stream5_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.UART4_IRQHandler) for UART4_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler) for DMA2_Stream2_IRQHandler
    startup_stm32f407xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(.text.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(HEAP) for Heap_Mem
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(STACK) for Stack_Mem
    main.o(.text.main) refers to stm32f4xx_hal.o(.text.HAL_Init) for HAL_Init
    main.o(.text.main) refers to main.o(.text.SystemClock_Config) for SystemClock_Config
    main.o(.text.main) refers to gpio.o(.text.MX_GPIO_Init) for MX_GPIO_Init
    main.o(.text.main) refers to dma.o(.text.MX_DMA_Init) for MX_DMA_Init
    main.o(.text.main) refers to usart.o(.text.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(.text.main) refers to usart.o(.text.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(.text.main) refers to usart.o(.text.MX_UART4_Init) for MX_UART4_Init
    main.o(.text.main) refers to uart.o(.bss..L_MergedGlobals) for uart1_ring
    main.o(.text.main) refers to usart.o(.bss.ringbuffer_pool_1) for ringbuffer_pool_1
    main.o(.text.main) refers to ringbuffer.o(.text.rt_ringbuffer_init) for rt_ringbuffer_init
    main.o(.text.main) refers to uart.o(.text.uart1_proc) for uart1_proc
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    main.o(.text.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(.text.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(.text.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(.ARM.exidx.text.SystemClock_Config) refers to main.o(.text.SystemClock_Config) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.Error_Handler) refers to main.o(.text.Error_Handler) for [Anonymous Symbol]
    gpio.o(.ARM.exidx.text.MX_GPIO_Init) refers to gpio.o(.text.MX_GPIO_Init) for [Anonymous Symbol]
    dma.o(.text.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(.text.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    dma.o(.ARM.exidx.text.MX_DMA_Init) refers to dma.o(.text.MX_DMA_Init) for [Anonymous Symbol]
    usart.o(.text.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss.uart_1_buf) for uart_1_buf
    usart.o(.text.HAL_UARTEx_RxEventCallback) refers to uart.o(.bss..L_MergedGlobals) for uart1_ring
    usart.o(.text.HAL_UARTEx_RxEventCallback) refers to ringbuffer.o(.text.rt_ringbuffer_put) for rt_ringbuffer_put
    usart.o(.text.HAL_UARTEx_RxEventCallback) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback) refers to usart.o(.text.HAL_UARTEx_RxEventCallback) for [Anonymous Symbol]
    usart.o(.text.MX_UART4_Init) refers to usart.o(.bss.huart4) for huart4
    usart.o(.text.MX_UART4_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_UART4_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.ARM.exidx.text.MX_UART4_Init) refers to usart.o(.text.MX_UART4_Init) for [Anonymous Symbol]
    usart.o(.text.MX_USART1_UART_Init) refers to usart.o(.bss.huart1) for huart1
    usart.o(.text.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_USART1_UART_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.text.MX_USART1_UART_Init) refers to usart.o(.bss.uart_1_buf) for uart_1_buf
    usart.o(.text.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(.text.MX_USART1_UART_Init) refers to usart.o(.bss.hdma_usart1_rx) for hdma_usart1_rx
    usart.o(.ARM.exidx.text.MX_USART1_UART_Init) refers to usart.o(.text.MX_USART1_UART_Init) for [Anonymous Symbol]
    usart.o(.text.MX_USART2_UART_Init) refers to usart.o(.bss.huart2) for huart2
    usart.o(.text.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_USART2_UART_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.ARM.exidx.text.MX_USART2_UART_Init) refers to usart.o(.text.MX_USART2_UART_Init) for [Anonymous Symbol]
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(.text.HAL_UART_MspInit) refers to usart.o(.bss.hdma_uart4_rx) for hdma_uart4_rx
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(.text.HAL_UART_MspInit) refers to usart.o(.bss.hdma_usart1_rx) for hdma_usart1_rx
    usart.o(.text.HAL_UART_MspInit) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.text.HAL_UART_MspInit) refers to usart.o(.bss.hdma_usart2_rx) for hdma_usart2_rx
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(.ARM.exidx.text.HAL_UART_MspInit) refers to usart.o(.text.HAL_UART_MspInit) for [Anonymous Symbol]
    usart.o(.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(.ARM.exidx.text.HAL_UART_MspDeInit) refers to usart.o(.text.HAL_UART_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.NMI_Handler) refers to stm32f4xx_it.o(.text.NMI_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.HardFault_Handler) refers to stm32f4xx_it.o(.text.HardFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.MemManage_Handler) refers to stm32f4xx_it.o(.text.MemManage_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.BusFault_Handler) refers to stm32f4xx_it.o(.text.BusFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.UsageFault_Handler) refers to stm32f4xx_it.o(.text.UsageFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.SVC_Handler) refers to stm32f4xx_it.o(.text.SVC_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.DebugMon_Handler) refers to stm32f4xx_it.o(.text.DebugMon_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.PendSV_Handler) refers to stm32f4xx_it.o(.text.PendSV_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.SysTick_Handler) refers to stm32f4xx_hal.o(.text.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(.ARM.exidx.text.SysTick_Handler) refers to stm32f4xx_it.o(.text.SysTick_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.DMA1_Stream2_IRQHandler) refers to usart.o(.bss.hdma_uart4_rx) for hdma_uart4_rx
    stm32f4xx_it.o(.text.DMA1_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.DMA1_Stream2_IRQHandler) refers to stm32f4xx_it.o(.text.DMA1_Stream2_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler) refers to usart.o(.bss.hdma_usart2_rx) for hdma_usart2_rx
    stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.DMA1_Stream5_IRQHandler) refers to stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.USART1_IRQHandler) refers to usart.o(.bss.huart1) for huart1
    stm32f4xx_it.o(.text.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(.text.USART1_IRQHandler) refers to usart.o(.bss.uart_1_buf) for uart_1_buf
    stm32f4xx_it.o(.text.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    stm32f4xx_it.o(.text.USART1_IRQHandler) refers to usart.o(.bss.hdma_usart1_rx) for hdma_usart1_rx
    stm32f4xx_it.o(.ARM.exidx.text.USART1_IRQHandler) refers to stm32f4xx_it.o(.text.USART1_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.USART2_IRQHandler) refers to usart.o(.bss.huart2) for huart2
    stm32f4xx_it.o(.text.USART2_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.USART2_IRQHandler) refers to stm32f4xx_it.o(.text.USART2_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.UART4_IRQHandler) refers to usart.o(.bss.huart4) for huart4
    stm32f4xx_it.o(.text.UART4_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.UART4_IRQHandler) refers to stm32f4xx_it.o(.text.UART4_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler) refers to usart.o(.bss.hdma_usart1_rx) for hdma_usart1_rx
    stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.DMA2_Stream2_IRQHandler) refers to stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_msp.o(.ARM.exidx.text.HAL_MspInit) refers to stm32f4xx_hal_msp.o(.text.HAL_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspInit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(.text.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(.text.UART_SetConfig) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_SetConfig) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_LIN_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_LIN_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_DeInit) refers to usart.o(.text.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DeInit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.UART_Start_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMAError) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAError) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAPause) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAPause) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAResume) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAResume) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_GetRxEventType) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_GetRxEventType) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Abort) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmitCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceiveCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to usart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_Receive_IT) refers to usart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.text.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.UART_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ErrorCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxHalfCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxHalfCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_SendBreak) refers to stm32f4xx_hal_uart.o(.text.HAL_LIN_SendBreak) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnterMuteMode) refers to stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_EnterMuteMode) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_ExitMuteMode) refers to stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_ExitMuteMode) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableTransmitter) refers to stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableTransmitter) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableReceiver) refers to stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableReceiver) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetState) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetError) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) refers to usart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) refers to usart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for uwTickPrio
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_DisableCSS) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for uwTickPrio
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCC_DeInit) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Unlock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Lock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Lock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_Erase_Sector) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_FlushCaches) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_LockPin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ChangeMemory) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_ChangeMemory) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(.rodata.cst8) for DMA_CalcBaseAndBitshift.flagBitshiftOffset
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(.rodata.cst8) for DMA_CalcBaseAndBitshift.flagBitshiftOffset
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_RegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnablePVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisablePVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableFlashPowerDown) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableFlashPowerDown) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableFlashPowerDown) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableFlashPowerDown) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_GetVoltageRange) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_GetVoltageRange) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset) for __NVIC_SystemReset
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_Disable) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_Enable) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_EnableRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_EnableRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_DisableRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_DisableRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_CORTEX_ClearEvent) refers to stm32f4xx_hal_cortex.o(.text.HAL_CORTEX_ClearEvent) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriority) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetActive) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal_msp.o(.text.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_Init) refers to stm32f4xx_hal.o(.text.HAL_Init) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_InitTick) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspInit) refers to stm32f4xx_hal.o(.text.HAL_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_DeInit) refers to stm32f4xx_hal.o(.text.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DeInit) refers to stm32f4xx_hal.o(.text.HAL_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspDeInit) refers to stm32f4xx_hal.o(.text.HAL_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_IncTick) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.text.HAL_IncTick) refers to stm32f4xx_hal.o(.bss.uwTick) for uwTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_IncTick) refers to stm32f4xx_hal.o(.text.HAL_IncTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTick) refers to stm32f4xx_hal.o(.bss.uwTick) for uwTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTick) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.text.HAL_GetTickPrio) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_SetTickFreq) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_GetTickFreq) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_Delay) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(.text.HAL_Delay) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_Delay) refers to stm32f4xx_hal.o(.text.HAL_Delay) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_SuspendTick) refers to stm32f4xx_hal.o(.text.HAL_SuspendTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_ResumeTick) refers to stm32f4xx_hal.o(.text.HAL_ResumeTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion) refers to stm32f4xx_hal.o(.text.HAL_GetHalVersion) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetREVID) refers to stm32f4xx_hal.o(.text.HAL_GetREVID) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetDEVID) refers to stm32f4xx_hal.o(.text.HAL_GetDEVID) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableCompensationCell) refers to stm32f4xx_hal.o(.text.HAL_EnableCompensationCell) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableCompensationCell) refers to stm32f4xx_hal.o(.text.HAL_DisableCompensationCell) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw0) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw1) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw2) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetHandle) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetPending) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearPending) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI) for [Anonymous Symbol]
    system_stm32f4xx.o(.ARM.exidx.text.SystemInit) refers to system_stm32f4xx.o(.text.SystemInit) for [Anonymous Symbol]
    system_stm32f4xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    system_stm32f4xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    system_stm32f4xx.o(.ARM.exidx.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.text.SystemCoreClockUpdate) for [Anonymous Symbol]
    ringbuffer.o(.text.rt_ringbuffer_init) refers to ringbuffer.o(.rodata.str1.1) for .L.str.1
    ringbuffer.o(.text.rt_ringbuffer_init) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_init) refers to ringbuffer.o(.text.rt_ringbuffer_init) for [Anonymous Symbol]
    ringbuffer.o(.text.rt_ringbuffer_put) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(.text.rt_ringbuffer_put) refers to ringbuffer.o(.rodata.str1.1) for .L.str.3
    ringbuffer.o(.text.rt_ringbuffer_put) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_put) refers to ringbuffer.o(.text.rt_ringbuffer_put) for [Anonymous Symbol]
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_data_len) refers to ringbuffer.o(.text.rt_ringbuffer_data_len) for [Anonymous Symbol]
    ringbuffer.o(.text.rt_ringbuffer_put_force) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(.text.rt_ringbuffer_put_force) refers to ringbuffer.o(.rodata.str1.1) for .L.str.3
    ringbuffer.o(.text.rt_ringbuffer_put_force) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_put_force) refers to ringbuffer.o(.text.rt_ringbuffer_put_force) for [Anonymous Symbol]
    ringbuffer.o(.text.rt_ringbuffer_get) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(.text.rt_ringbuffer_get) refers to ringbuffer.o(.rodata.str1.1) for .L.str.3
    ringbuffer.o(.text.rt_ringbuffer_get) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_get) refers to ringbuffer.o(.text.rt_ringbuffer_get) for [Anonymous Symbol]
    ringbuffer.o(.text.rt_ringbuffer_peek) refers to ringbuffer.o(.rodata.str1.1) for .L.str.3
    ringbuffer.o(.text.rt_ringbuffer_peek) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_peek) refers to ringbuffer.o(.text.rt_ringbuffer_peek) for [Anonymous Symbol]
    ringbuffer.o(.text.rt_ringbuffer_putchar) refers to ringbuffer.o(.rodata.str1.1) for .L.str.3
    ringbuffer.o(.text.rt_ringbuffer_putchar) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_putchar) refers to ringbuffer.o(.text.rt_ringbuffer_putchar) for [Anonymous Symbol]
    ringbuffer.o(.text.rt_ringbuffer_putchar_force) refers to ringbuffer.o(.rodata.str1.1) for .L.str.3
    ringbuffer.o(.text.rt_ringbuffer_putchar_force) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_putchar_force) refers to ringbuffer.o(.text.rt_ringbuffer_putchar_force) for [Anonymous Symbol]
    ringbuffer.o(.text.rt_ringbuffer_getchar) refers to ringbuffer.o(.rodata.str1.1) for .L.str.3
    ringbuffer.o(.text.rt_ringbuffer_getchar) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_getchar) refers to ringbuffer.o(.text.rt_ringbuffer_getchar) for [Anonymous Symbol]
    ringbuffer.o(.text.rt_ringbuffer_reset) refers to ringbuffer.o(.rodata.str1.1) for .L.str.3
    ringbuffer.o(.text.rt_ringbuffer_reset) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_reset) refers to ringbuffer.o(.text.rt_ringbuffer_reset) for [Anonymous Symbol]
    uart.o(.text.uart1_proc) refers to uart.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    uart.o(.text.uart1_proc) refers to ringbuffer.o(.text.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    uart.o(.text.uart1_proc) refers to ringbuffer.o(.text.rt_ringbuffer_get) for rt_ringbuffer_get
    uart.o(.text.uart1_proc) refers to usart.o(.bss.huart1) for huart1
    uart.o(.text.uart1_proc) refers to uart.o(.text.my_printf) for my_printf
    uart.o(.text.uart1_proc) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    uart.o(.ARM.exidx.text.uart1_proc) refers to uart.o(.text.uart1_proc) for [Anonymous Symbol]
    uart.o(.text.my_printf) refers to vsnprintf.o(.text) for vsnprintf
    uart.o(.text.my_printf) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    uart.o(.ARM.exidx.text.my_printf) refers to uart.o(.text.my_printf) for [Anonymous Symbol]
    vsnprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsnprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsnprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsnprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsnprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsnprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsnprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsnprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsnprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsnprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsnprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsnprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsnprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsnprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsnprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsnprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsnprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsnprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsnprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsnprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsnprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsnprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsnprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsnprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsnprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsnprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsnprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsnprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsnprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsnprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsnprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsnprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsnprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsnprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsnprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsnprintf.o(.text) refers to _snputc.o(.text) for _snputc
    assert.o(.text) refers to assert_puts.o(.text) for __assert_puts
    assert.o(.text) refers to abort.o(.text) for abort
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _c16rtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    abort.o(.text) refers to defsig_abrt_outer.o(.text) for __rt_SIGABRT
    abort.o(.text) refers (Weak) to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    abort.o(.text) refers to sys_exit.o(.text) for _sys_exit
    assert_puts.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_wrch_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec_accurate.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec_accurate.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec_accurate.o(.text) refers to btod_accurate.o(.text) for _btod_main
    _printf_fp_dec_accurate.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec_accurate.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec_accurate.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _c16rtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    defsig_abrt_outer.o(.text) refers to defsig_abrt_inner.o(.text) for __rt_SIGABRT_inner
    defsig_abrt_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_abrt_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f407xx.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod_accurate.o(.text) refers to btod_accurate_common.o(.text) for _btod_common
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000018) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000018) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7em.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000034) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000006) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000010) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_relocate_pie_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000035) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000027) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_user_alloc_1
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    btod_accurate_common.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000014) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000014) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7em.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000011) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$0000001A) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000028) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000029) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    defsig.o(CL$$defsig) refers to defsig_abrt_inner.o(.text) for __rt_SIGABRT_inner
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.ARM.exidx.text.SystemClock_Config), (8 bytes).
    Removing main.o(.ARM.exidx.text.Error_Handler), (8 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing gpio.o(.text), (0 bytes).
    Removing gpio.o(.ARM.exidx.text.MX_GPIO_Init), (8 bytes).
    Removing dma.o(.text), (0 bytes).
    Removing dma.o(.ARM.exidx.text.MX_DMA_Init), (8 bytes).
    Removing usart.o(.text), (0 bytes).
    Removing usart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback), (8 bytes).
    Removing usart.o(.ARM.exidx.text.MX_UART4_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.MX_USART1_UART_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.MX_USART2_UART_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.HAL_UART_MspInit), (8 bytes).
    Removing usart.o(.text.HAL_UART_MspDeInit), (124 bytes).
    Removing usart.o(.ARM.exidx.text.HAL_UART_MspDeInit), (8 bytes).
    Removing stm32f4xx_it.o(.text), (0 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.NMI_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.HardFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.MemManage_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.BusFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.UsageFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.SVC_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DebugMon_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.PendSV_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DMA1_Stream2_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DMA1_Stream5_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.USART1_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.USART2_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.UART4_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DMA2_Stream2_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_msp.o(.text), (0 bytes).
    Removing stm32f4xx_hal_msp.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text), (0 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init), (104 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_LIN_Init), (120 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init), (128 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DeInit), (54 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DeInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Receive), (558 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_IT), (62 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT), (84 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_Start_Receive_IT), (50 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA), (212 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt), (176 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATransmitCplt), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt), (6 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAError), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA), (384 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA), (336 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DMAPause), (342 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAPause), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DMAResume), (348 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAResume), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop), (540 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAStop), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle), (478 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_IT), (200 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_GetRxEventType), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Abort), (482 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit), (208 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive), (366 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT), (516 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback), (42 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback), (42 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT), (214 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback), (16 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxOnlyAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmitCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT), (372 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback), (18 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxOnlyAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceiveCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAAbortOnError), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_LIN_SendBreak), (114 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_SendBreak), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_EnterMuteMode), (116 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnterMuteMode), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_ExitMuteMode), (116 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_ExitMuteMode), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableTransmitter), (44 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableTransmitter), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableReceiver), (46 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableReceiver), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_GetState), (14 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetState), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetError), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAReceiveCplt), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text), (0 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig), (168 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig), (150 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig), (62 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig), (330 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig), (50 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq), (88 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S), (106 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnablePLLI2S), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisablePLLI2S), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit), (296 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text), (0 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program), (218 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation), (250 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT), (240 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler), (312 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Unlock), (46 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock), (42 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.bss.pFlash), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase), (394 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector), (70 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_Erase_Sector), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches), (88 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_FlushCaches), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT), (176 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram), (184 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig), (56 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig), (8 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.text), (0 bytes).
    Removing stm32f4xx_hal_gpio.o(.text), (0 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit), (462 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin), (10 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin), (10 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_LockPin), (44 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler), (22 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart), (114 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart_IT), (1236 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart_IT), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_ChangeMemory), (20 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ChangeMemory), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit), (136 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Start), (114 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer), (454 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_RegisterCallback), (46 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback), (156 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text), (0 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DeInit), (26 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD), (130 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode), (84 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg), (56 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableBkUpReg), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg), (56 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableBkUpReg), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableFlashPowerDown), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableFlashPowerDown), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_GetVoltageRange), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling), (102 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ControlVoltageScaling), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_Disable), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_Enable), (32 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_EnableRegion), (22 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_EnableRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_DisableRegion), (22 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_DisableRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion), (94 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_CORTEX_ClearEvent), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriority), (88 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ), (38 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetActive), (38 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback), (8 bytes).
    Removing stm32f4xx_hal.o(.text), (0 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_Init), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_InitTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DeInit), (68 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DeInit), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_IncTick), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_SetTickFreq), (38 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_Delay), (40 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_Delay), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_SuspendTick), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_SuspendTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_ResumeTick), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_ResumeTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetHalVersion), (10 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetREVID), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetREVID), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetDEVID), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_EnableCompensationCell), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableCompensationCell), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DisableCompensationCell), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableCompensationCell), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text), (0 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine), (186 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine), (148 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine), (140 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback), (12 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_IRQHandler), (42 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetPending), (28 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI), (24 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI), (8 bytes).
    Removing system_stm32f4xx.o(.text), (0 bytes).
    Removing system_stm32f4xx.o(.ARM.exidx.text.SystemInit), (8 bytes).
    Removing system_stm32f4xx.o(.text.SystemCoreClockUpdate), (134 bytes).
    Removing system_stm32f4xx.o(.ARM.exidx.text.SystemCoreClockUpdate), (8 bytes).
    Removing ringbuffer.o(.text), (0 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_init), (8 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_put), (8 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_data_len), (8 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_put_force), (234 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_put_force), (8 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_get), (8 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_peek), (158 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_peek), (8 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_putchar), (124 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_putchar), (8 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_putchar_force), (128 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_putchar_force), (8 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_getchar), (140 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_getchar), (8 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_reset), (34 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_reset), (8 bytes).
    Removing uart.o(.text), (0 bytes).
    Removing uart.o(.ARM.exidx.text.uart1_proc), (8 bytes).
    Removing uart.o(.ARM.exidx.text.my_printf), (8 bytes).

439 unused section(s) (total 18736 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command_hlt.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/assert.c                         0x00000000   Number         0  assert.o ABSOLUTE
    ../clib/assert.c                         0x00000000   Number         0  assert_puts.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/btod_accurate.c                  0x00000000   Number         0  btod_accurate.o ABSOLUTE
    ../clib/btod_accurate.c                  0x00000000   Number         0  btod_accurate_common.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _c16rtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7em.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsnprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec_accurate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  abort.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    dma.c                                    0x00000000   Number         0  dma.o ABSOLUTE
    gpio.c                                   0x00000000   Number         0  gpio.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    ringbuffer.c                             0x00000000   Number         0  ringbuffer.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    stm32f4xx_hal.c                          0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    stm32f4xx_hal_cortex.c                   0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    stm32f4xx_hal_dma.c                      0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    stm32f4xx_hal_dma_ex.c                   0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    stm32f4xx_hal_exti.c                     0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    stm32f4xx_hal_flash.c                    0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    stm32f4xx_hal_flash_ex.c                 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    stm32f4xx_hal_flash_ramfunc.c            0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    stm32f4xx_hal_gpio.c                     0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    stm32f4xx_hal_msp.c                      0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    stm32f4xx_hal_pwr.c                      0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    stm32f4xx_hal_pwr_ex.c                   0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    stm32f4xx_hal_rcc.c                      0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    stm32f4xx_hal_rcc_ex.c                   0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    stm32f4xx_hal_uart.c                     0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    uart.c                                   0x00000000   Number         0  uart.o ABSOLUTE
    usart.c                                  0x00000000   Number         0  usart.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       92  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001ec   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_null                           0x08000208   Section        2  __scatter.o(!!handler_null)
    !!handler_zi                             0x0800020c   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x08000228   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000001  0x08000228   Section        6  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    .ARM.Collect$$_printf_percent$$00000002  0x0800022e   Section        6  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    .ARM.Collect$$_printf_percent$$00000003  0x08000234   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000004  0x0800023a   Section        6  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    .ARM.Collect$$_printf_percent$$00000005  0x08000240   Section        6  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    .ARM.Collect$$_printf_percent$$00000006  0x08000246   Section        6  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    .ARM.Collect$$_printf_percent$$00000007  0x0800024c   Section       10  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    .ARM.Collect$$_printf_percent$$00000008  0x08000256   Section        6  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    .ARM.Collect$$_printf_percent$$00000009  0x0800025c   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x08000262   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000B  0x08000268   Section        6  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    .ARM.Collect$$_printf_percent$$0000000C  0x0800026e   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$0000000D  0x08000274   Section        6  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    .ARM.Collect$$_printf_percent$$0000000E  0x0800027a   Section        6  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    .ARM.Collect$$_printf_percent$$0000000F  0x08000280   Section        6  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    .ARM.Collect$$_printf_percent$$00000010  0x08000286   Section        6  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    .ARM.Collect$$_printf_percent$$00000011  0x0800028c   Section        6  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    .ARM.Collect$$_printf_percent$$00000012  0x08000292   Section       10  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    .ARM.Collect$$_printf_percent$$00000013  0x0800029c   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x080002a2   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000015  0x080002a8   Section        6  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    .ARM.Collect$$_printf_percent$$00000016  0x080002ae   Section        6  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    .ARM.Collect$$_printf_percent$$00000017  0x080002b4   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x080002b8   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x080002ba   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x080002be   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000006          0x080002be   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    .ARM.Collect$$libinit$$0000000C          0x080002be   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080002be   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000010          0x080002be   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    .ARM.Collect$$libinit$$00000011          0x080002be   Section        6  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x080002c4   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000014          0x080002c4   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000014)
    .ARM.Collect$$libinit$$00000015          0x080002d0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x080002d0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000018          0x080002d0   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000018)
    .ARM.Collect$$libinit$$00000019          0x080002da   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080002da   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080002da   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080002da   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080002da   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080002da   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080002da   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$00000027          0x080002da   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    .ARM.Collect$$libinit$$0000002E          0x080002da   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080002da   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080002da   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000034          0x080002da   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    .ARM.Collect$$libinit$$00000035          0x080002da   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000035)
    .ARM.Collect$$libshutdown$$00000000      0x080002dc   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080002de   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080002de   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x080002de   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x080002de   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x080002de   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x080002de   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x080002de   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x080002e0   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080002e0   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080002e0   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080002e6   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080002e6   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080002ea   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080002ea   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080002f2   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080002f4   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080002f4   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080002f8   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    $v0                                      0x08000300   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x08000300   Section       64  startup_stm32f407xx.o(.text)
    .text                                    0x08000340   Section      240  lludivv7m.o(.text)
    .text                                    0x08000430   Section        0  vsnprintf.o(.text)
    .text                                    0x0800046c   Section        0  assert.o(.text)
    .text                                    0x080004ec   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x08000576   Section       78  rt_memclr_w.o(.text)
    .text                                    0x080005c4   Section        0  heapauxi.o(.text)
    .text                                    0x080005ca   Section        0  _printf_pad.o(.text)
    .text                                    0x08000618   Section        0  _printf_truncate.o(.text)
    .text                                    0x0800063c   Section        0  _printf_str.o(.text)
    .text                                    0x08000690   Section        0  _printf_dec.o(.text)
    .text                                    0x08000708   Section        0  _printf_charcount.o(.text)
    _printf_input_char                       0x08000731   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000730   Section        0  _printf_char_common.o(.text)
    .text                                    0x08000760   Section        0  _snputc.o(.text)
    .text                                    0x08000770   Section        0  _printf_wctomb.o(.text)
    .text                                    0x0800082c   Section        0  _printf_longlong_dec.o(.text)
    _printf_longlong_oct_internal            0x080008a9   Thumb Code     0  _printf_oct_int_ll.o(.text)
    .text                                    0x080008a8   Section        0  _printf_oct_int_ll.o(.text)
    _printf_hex_common                       0x08000919   Thumb Code     0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x08000918   Section        0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x080009ac   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x08000b34   Section        0  abort.o(.text)
    .text                                    0x08000b4a   Section        0  assert_puts.o(.text)
    .text                                    0x08000b5e   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x08000bc2   Section        0  sys_wrch.o(.text)
    .text                                    0x08000bd0   Section        0  sys_exit.o(.text)
    .text                                    0x08000bdc   Section      138  lludiv10.o(.text)
    .text                                    0x08000c66   Section        0  _printf_intcommon.o(.text)
    _fp_digits                               0x08000d19   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08000d18   Section        0  _printf_fp_dec.o(.text)
    .text                                    0x08001134   Section        0  _printf_fp_hex.o(.text)
    .text                                    0x08001430   Section        0  _printf_char.o(.text)
    .text                                    0x0800145c   Section        0  _printf_wchar.o(.text)
    .text                                    0x08001488   Section        0  _c16rtomb.o(.text)
    .text                                    0x080014d0   Section        0  defsig_abrt_outer.o(.text)
    .text                                    0x080014de   Section        2  use_no_semi.o(.text)
    .text                                    0x080014e0   Section        0  indicate_semi.o(.text)
    .text                                    0x080014e0   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x0800152c   Section       16  rt_ctype_table.o(.text)
    .text                                    0x0800153c   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08001544   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x080015c4   Section        0  bigflt0.o(.text)
    .text                                    0x080016a8   Section        0  exit.o(.text)
    .text                                    0x080016ba   Section        0  defsig_exit.o(.text)
    .text                                    0x080016c4   Section        0  defsig_abrt_inner.o(.text)
    .text                                    0x080016f4   Section        8  libspace.o(.text)
    .text                                    0x080016fc   Section        0  defsig_general.o(.text)
    .text                                    0x08001730   Section      124  strcmpv7em.o(.text)
    [Anonymous Symbol]                       0x080017ac   Section        0  stm32f4xx_it.o(.text.BusFault_Handler)
    [Anonymous Symbol]                       0x080017b0   Section        0  stm32f4xx_it.o(.text.DMA1_Stream2_IRQHandler)
    [Anonymous Symbol]                       0x080017bc   Section        0  stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler)
    [Anonymous Symbol]                       0x080017c8   Section        0  stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler)
    [Anonymous Symbol]                       0x080017d4   Section        0  stm32f4xx_it.o(.text.DebugMon_Handler)
    [Anonymous Symbol]                       0x080017d8   Section        0  main.o(.text.Error_Handler)
    [Anonymous Symbol]                       0x080017e0   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort)
    [Anonymous Symbol]                       0x08001870   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT)
    [Anonymous Symbol]                       0x08001894   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler)
    [Anonymous Symbol]                       0x08001a58   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Init)
    [Anonymous Symbol]                       0x08001bbc   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT)
    [Anonymous Symbol]                       0x08001c60   Section        0  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init)
    [Anonymous Symbol]                       0x08001e00   Section        0  stm32f4xx_hal.o(.text.HAL_GetTick)
    [Anonymous Symbol]                       0x08001e0c   Section        0  stm32f4xx_hal.o(.text.HAL_IncTick)
    [Anonymous Symbol]                       0x08001e28   Section        0  stm32f4xx_hal.o(.text.HAL_Init)
    [Anonymous Symbol]                       0x08001e60   Section        0  stm32f4xx_hal.o(.text.HAL_InitTick)
    [Anonymous Symbol]                       0x08001ea8   Section        0  stm32f4xx_hal_msp.o(.text.HAL_MspInit)
    [Anonymous Symbol]                       0x08001ee0   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x08001f04   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    [Anonymous Symbol]                       0x08001f5c   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    [Anonymous Symbol]                       0x08001f7c   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    [Anonymous Symbol]                       0x080020e0   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq)
    [Anonymous Symbol]                       0x08002108   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq)
    [Anonymous Symbol]                       0x08002130   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    [Anonymous Symbol]                       0x0800219c   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    [Anonymous Symbol]                       0x08002548   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    [Anonymous Symbol]                       0x08002574   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA)
    [Anonymous Symbol]                       0x08002764   Section        0  usart.o(.text.HAL_UARTEx_RxEventCallback)
    [Anonymous Symbol]                       0x08002798   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback)
    [Anonymous Symbol]                       0x0800279c   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler)
    [Anonymous Symbol]                       0x08002d30   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_Init)
    [Anonymous Symbol]                       0x08002d90   Section        0  usart.o(.text.HAL_UART_MspInit)
    [Anonymous Symbol]                       0x08002f8c   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback)
    [Anonymous Symbol]                       0x08002f90   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback)
    [Anonymous Symbol]                       0x08002f94   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit)
    [Anonymous Symbol]                       0x08003128   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback)
    [Anonymous Symbol]                       0x0800312c   Section        0  stm32f4xx_it.o(.text.HardFault_Handler)
    [Anonymous Symbol]                       0x08003130   Section        0  dma.o(.text.MX_DMA_Init)
    [Anonymous Symbol]                       0x0800319c   Section        0  gpio.o(.text.MX_GPIO_Init)
    [Anonymous Symbol]                       0x080031d4   Section        0  usart.o(.text.MX_UART4_Init)
    [Anonymous Symbol]                       0x08003210   Section        0  usart.o(.text.MX_USART1_UART_Init)
    [Anonymous Symbol]                       0x08003270   Section        0  usart.o(.text.MX_USART2_UART_Init)
    [Anonymous Symbol]                       0x080032ac   Section        0  stm32f4xx_it.o(.text.MemManage_Handler)
    [Anonymous Symbol]                       0x080032b0   Section        0  stm32f4xx_it.o(.text.NMI_Handler)
    [Anonymous Symbol]                       0x080032b4   Section        0  stm32f4xx_it.o(.text.PendSV_Handler)
    [Anonymous Symbol]                       0x080032b8   Section        0  stm32f4xx_it.o(.text.SVC_Handler)
    [Anonymous Symbol]                       0x080032bc   Section        0  stm32f4xx_it.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x080032c0   Section        0  main.o(.text.SystemClock_Config)
    [Anonymous Symbol]                       0x08003368   Section        0  system_stm32f4xx.o(.text.SystemInit)
    [Anonymous Symbol]                       0x0800337c   Section        0  stm32f4xx_it.o(.text.UART4_IRQHandler)
    UART_DMAAbortOnError                     0x08003389   Thumb Code    10  stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError)
    [Anonymous Symbol]                       0x08003388   Section        0  stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError)
    UART_DMAError                            0x08003395   Thumb Code   380  stm32f4xx_hal_uart.o(.text.UART_DMAError)
    [Anonymous Symbol]                       0x08003394   Section        0  stm32f4xx_hal_uart.o(.text.UART_DMAError)
    UART_DMAReceiveCplt                      0x08003511   Thumb Code   350  stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt)
    [Anonymous Symbol]                       0x08003510   Section        0  stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt)
    UART_DMARxHalfCplt                       0x08003671   Thumb Code    24  stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt)
    [Anonymous Symbol]                       0x08003670   Section        0  stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt)
    UART_Receive_IT                          0x08003689   Thumb Code   254  stm32f4xx_hal_uart.o(.text.UART_Receive_IT)
    [Anonymous Symbol]                       0x08003688   Section        0  stm32f4xx_hal_uart.o(.text.UART_Receive_IT)
    UART_SetConfig                           0x08003789   Thumb Code   230  stm32f4xx_hal_uart.o(.text.UART_SetConfig)
    [Anonymous Symbol]                       0x08003788   Section        0  stm32f4xx_hal_uart.o(.text.UART_SetConfig)
    [Anonymous Symbol]                       0x08003870   Section        0  stm32f4xx_it.o(.text.USART1_IRQHandler)
    [Anonymous Symbol]                       0x080038a4   Section        0  stm32f4xx_it.o(.text.USART2_IRQHandler)
    [Anonymous Symbol]                       0x080038b0   Section        0  stm32f4xx_it.o(.text.UsageFault_Handler)
    [Anonymous Symbol]                       0x080038b4   Section        0  main.o(.text.main)
    [Anonymous Symbol]                       0x080038f0   Section        0  uart.o(.text.my_printf)
    [Anonymous Symbol]                       0x08003930   Section        0  ringbuffer.o(.text.rt_ringbuffer_data_len)
    [Anonymous Symbol]                       0x08003974   Section        0  ringbuffer.o(.text.rt_ringbuffer_get)
    [Anonymous Symbol]                       0x08003a48   Section        0  ringbuffer.o(.text.rt_ringbuffer_init)
    [Anonymous Symbol]                       0x08003a98   Section        0  ringbuffer.o(.text.rt_ringbuffer_put)
    [Anonymous Symbol]                       0x08003b68   Section        0  uart.o(.text.uart1_proc)
    CL$$btod_d2e                             0x08003bb8   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08003bf6   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08003c3c   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08003c9c   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x08003fd4   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x080040b0   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x080040da   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x08004104   Section      580  btod.o(CL$$btod_mult_common)
    i.__ARM_fpclassify                       0x08004348   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i._is_digit                              0x08004378   Section        0  __printf_wp.o(i._is_digit)
    locale$$code                             0x08004388   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x080043b4   Section       44  lc_ctype_c.o(locale$$code)
    $v0                                      0x080043e0   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$fpinit                             0x080043e0   Section       26  fpinit.o(x$fpl$fpinit)
    $v0                                      0x080043fa   Number         0  printf1.o(x$fpl$printf1)
    x$fpl$printf1                            0x080043fa   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x080043fe   Number         0  printf2.o(x$fpl$printf2)
    x$fpl$printf2                            0x080043fe   Section        4  printf2.o(x$fpl$printf2)
    x$fpl$usenofp                            0x08004402   Section        0  usenofp.o(x$fpl$usenofp)
    initial_mbstate                          0x08004404   Data           8  _printf_wctomb.o(.constdata)
    .constdata                               0x08004404   Section        8  _printf_wctomb.o(.constdata)
    uc_hextab                                0x0800440c   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    .constdata                               0x0800440c   Section       40  _printf_hex_int_ll_ptr.o(.constdata)
    lc_hextab                                0x08004420   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    maptable                                 0x08004434   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x08004434   Section       17  __printf_flags_ss_wp.o(.constdata)
    lc_hextab                                0x08004445   Data          19  _printf_fp_hex.o(.constdata)
    .constdata                               0x08004445   Section       38  _printf_fp_hex.o(.constdata)
    uc_hextab                                0x08004458   Data          19  _printf_fp_hex.o(.constdata)
    tenpwrs_x                                0x0800446c   Data          60  bigflt0.o(.constdata)
    .constdata                               0x0800446c   Section      148  bigflt0.o(.constdata)
    tenpwrs_i                                0x080044a8   Data          64  bigflt0.o(.constdata)
    DMA_CalcBaseAndBitshift.flagBitshiftOffset 0x08004518   Data           8  stm32f4xx_hal_dma.o(.rodata.cst8)
    [Anonymous Symbol]                       0x08004518   Section        0  stm32f4xx_hal_dma.o(.rodata.cst8)
    .L.str.3                                 0x08004520   Data          14  ringbuffer.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x08004520   Section        0  ringbuffer.o(.rodata.str1.1)
    .L.str.1                                 0x0800452e   Data          20  ringbuffer.o(.rodata.str1.1)
    locale$$data                             0x08004564   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x08004568   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x08004570   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x0800457c   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x0800457e   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x0800457f   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x08004580   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x08004580   Section      272  lc_ctype_c.o(locale$$data)
    __lcctype_c_name                         0x08004584   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x0800458c   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x08004690   Data           0  lc_ctype_c.o(locale$$data)
    .L_MergedGlobals                         0x20000000   Data           8  stm32f4xx_hal.o(.data..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000000   Section        0  stm32f4xx_hal.o(.data..L_MergedGlobals)
    .bss                                     0x20000010   Section       96  libspace.o(.bss)
    .L_MergedGlobals                         0x20000070   Data          76  uart.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000070   Section        0  uart.o(.bss..L_MergedGlobals)
    Heap_Mem                                 0x20000338   Data         512  startup_stm32f407xx.o(HEAP)
    HEAP                                     0x20000338   Section      512  startup_stm32f407xx.o(HEAP)
    Stack_Mem                                0x20000538   Data        1024  startup_stm32f407xx.o(STACK)
    STACK                                    0x20000538   Section     1024  startup_stm32f407xx.o(STACK)
    __initial_sp                             0x20000938   Data           0  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __arm_relocate_pie_                       - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    84  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_loop                       0x0800019b   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001ed   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_null                       0x08000209   Thumb Code     2  __scatter.o(!!handler_null)
    __scatterload_zeroinit                   0x0800020d   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_n                                0x08000229   Thumb Code     0  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    _printf_percent                          0x08000229   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_p                                0x0800022f   Thumb Code     0  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    _printf_f                                0x08000235   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_e                                0x0800023b   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    _printf_g                                0x08000241   Thumb Code     0  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    _printf_a                                0x08000247   Thumb Code     0  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    _printf_ll                               0x0800024d   Thumb Code     0  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    _printf_i                                0x08000257   Thumb Code     0  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    _printf_d                                0x0800025d   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x08000263   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_o                                0x08000269   Thumb Code     0  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    _printf_x                                0x0800026f   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_lli                              0x08000275   Thumb Code     0  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    _printf_lld                              0x0800027b   Thumb Code     0  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    _printf_llu                              0x08000281   Thumb Code     0  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    _printf_llo                              0x08000287   Thumb Code     0  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    _printf_llx                              0x0800028d   Thumb Code     0  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    _printf_l                                0x08000293   Thumb Code     0  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    _printf_c                                0x0800029d   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x080002a3   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_lc                               0x080002a9   Thumb Code     0  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    _printf_ls                               0x080002af   Thumb Code     0  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    _printf_percent_end                      0x080002b5   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x080002b9   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080002bb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x080002bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_common                  0x080002bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_preinit_1                  0x080002bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    __rt_lib_init_rand_1                     0x080002bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    __rt_lib_init_relocate_pie_1             0x080002bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_user_alloc_1               0x080002bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_lc_collate_1               0x080002c5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_ctype_2                 0x080002c5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000014)
    __rt_lib_init_lc_ctype_1                 0x080002d1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_monetary_1              0x080002d1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_numeric_2               0x080002d1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000018)
    __rt_lib_init_alloca_1                   0x080002db   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_argv_1                     0x080002db   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_atexit_1                   0x080002db   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_clock_1                    0x080002db   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_cpp_1                      0x080002db   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    __rt_lib_init_exceptions_1               0x080002db   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_fp_trap_1                  0x080002db   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_getenv_1                   0x080002db   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_lc_numeric_1               0x080002db   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_lc_time_1                  0x080002db   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_return                     0x080002db   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000035)
    __rt_lib_init_signal_1                   0x080002db   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_stdio_1                    0x080002db   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    __rt_lib_shutdown                        0x080002dd   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080002df   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080002df   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x080002df   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x080002df   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x080002df   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x080002df   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x080002df   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x080002e1   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080002e1   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080002e1   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080002e7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080002e7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080002eb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080002eb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080002f3   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080002f5   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080002f5   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080002f9   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000301   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM2_IRQHandler                          0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART5_IRQHandler                         0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART3_IRQHandler                        0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART6_IRQHandler                        0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    __user_initial_stackheap                 0x0800031d   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_uldivmod                         0x08000341   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x08000341   Thumb Code   240  lludivv7m.o(.text)
    vsnprintf                                0x08000431   Thumb Code    56  vsnprintf.o(.text)
    __aeabi_assert                           0x0800046d   Thumb Code    86  assert.o(.text)
    __assert                                 0x0800046d   Thumb Code     0  assert.o(.text)
    __aeabi_memcpy                           0x080004ed   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x080004ed   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x08000553   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memclr4                          0x08000577   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x08000577   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x08000577   Thumb Code     0  rt_memclr_w.o(.text)
    _memset_w                                0x0800057b   Thumb Code    74  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x080005c5   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080005c7   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080005c9   Thumb Code     2  heapauxi.o(.text)
    _printf_pre_padding                      0x080005cb   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x080005f7   Thumb Code    34  _printf_pad.o(.text)
    _printf_truncate_signed                  0x08000619   Thumb Code    18  _printf_truncate.o(.text)
    _printf_truncate_unsigned                0x0800062b   Thumb Code    18  _printf_truncate.o(.text)
    _printf_str                              0x0800063d   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x08000691   Thumb Code   104  _printf_dec.o(.text)
    _printf_charcount                        0x08000709   Thumb Code    40  _printf_charcount.o(.text)
    _printf_char_common                      0x0800073b   Thumb Code    32  _printf_char_common.o(.text)
    _snputc                                  0x08000761   Thumb Code    16  _snputc.o(.text)
    _printf_wctomb                           0x08000771   Thumb Code   182  _printf_wctomb.o(.text)
    _printf_longlong_dec                     0x0800082d   Thumb Code   108  _printf_longlong_dec.o(.text)
    _printf_longlong_oct                     0x080008a9   Thumb Code    68  _printf_oct_int_ll.o(.text)
    _printf_int_oct                          0x080008ed   Thumb Code    24  _printf_oct_int_ll.o(.text)
    _printf_ll_oct                           0x08000905   Thumb Code    12  _printf_oct_int_ll.o(.text)
    _printf_longlong_hex                     0x08000919   Thumb Code    86  _printf_hex_int_ll_ptr.o(.text)
    _printf_int_hex                          0x0800096f   Thumb Code    28  _printf_hex_int_ll_ptr.o(.text)
    _printf_ll_hex                           0x0800098b   Thumb Code    12  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_ptr                          0x08000997   Thumb Code    18  _printf_hex_int_ll_ptr.o(.text)
    __printf                                 0x080009ad   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    abort                                    0x08000b35   Thumb Code    22  abort.o(.text)
    __assert_puts                            0x08000b4b   Thumb Code    20  assert_puts.o(.text)
    __aeabi_memcpy4                          0x08000b5f   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x08000b5f   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x08000b5f   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x08000ba7   Thumb Code     0  rt_memcpy_w.o(.text)
    _ttywrch                                 0x08000bc3   Thumb Code    14  sys_wrch.o(.text)
    _sys_exit                                0x08000bd1   Thumb Code     8  sys_exit.o(.text)
    _ll_udiv10                               0x08000bdd   Thumb Code   138  lludiv10.o(.text)
    _printf_int_common                       0x08000c67   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_fp_dec_real                      0x08000ec9   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_fp_hex_real                      0x08001135   Thumb Code   756  _printf_fp_hex.o(.text)
    _printf_cs_common                        0x08001431   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08001445   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08001455   Thumb Code     8  _printf_char.o(.text)
    _printf_lcs_common                       0x0800145d   Thumb Code    20  _printf_wchar.o(.text)
    _printf_wchar                            0x08001471   Thumb Code    16  _printf_wchar.o(.text)
    _printf_wstring                          0x08001481   Thumb Code     8  _printf_wchar.o(.text)
    _c16rtomb                                0x08001489   Thumb Code    72  _c16rtomb.o(.text)
    _wcrtomb                                 0x08001489   Thumb Code     0  _c16rtomb.o(.text)
    __rt_SIGABRT                             0x080014d1   Thumb Code    14  defsig_abrt_outer.o(.text)
    __I$use$semihosting                      0x080014df   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x080014df   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x080014e1   Thumb Code     0  indicate_semi.o(.text)
    __user_setup_stackheap                   0x080014e1   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_ctype_table                         0x0800152d   Thumb Code    16  rt_ctype_table.o(.text)
    __rt_locale                              0x0800153d   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _printf_fp_infnan                        0x08001545   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x080015c5   Thumb Code   224  bigflt0.o(.text)
    exit                                     0x080016a9   Thumb Code    18  exit.o(.text)
    __sig_exit                               0x080016bb   Thumb Code    10  defsig_exit.o(.text)
    __rt_SIGABRT_inner                       0x080016c5   Thumb Code    14  defsig_abrt_inner.o(.text)
    __user_libspace                          0x080016f5   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x080016f5   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x080016f5   Thumb Code     0  libspace.o(.text)
    __default_signal_display                 0x080016fd   Thumb Code    50  defsig_general.o(.text)
    strcmp                                   0x08001731   Thumb Code   124  strcmpv7em.o(.text)
    BusFault_Handler                         0x080017ad   Thumb Code     2  stm32f4xx_it.o(.text.BusFault_Handler)
    DMA1_Stream2_IRQHandler                  0x080017b1   Thumb Code    12  stm32f4xx_it.o(.text.DMA1_Stream2_IRQHandler)
    DMA1_Stream5_IRQHandler                  0x080017bd   Thumb Code    12  stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler)
    DMA2_Stream2_IRQHandler                  0x080017c9   Thumb Code    12  stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler)
    DebugMon_Handler                         0x080017d5   Thumb Code     2  stm32f4xx_it.o(.text.DebugMon_Handler)
    Error_Handler                            0x080017d9   Thumb Code     6  main.o(.text.Error_Handler)
    HAL_DMA_Abort                            0x080017e1   Thumb Code   142  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08001871   Thumb Code    36  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08001895   Thumb Code   452  stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08001a59   Thumb Code   354  stm32f4xx_hal_dma.o(.text.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x08001bbd   Thumb Code   162  stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT)
    HAL_GPIO_Init                            0x08001c61   Thumb Code   414  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init)
    HAL_GetTick                              0x08001e01   Thumb Code    12  stm32f4xx_hal.o(.text.HAL_GetTick)
    HAL_IncTick                              0x08001e0d   Thumb Code    26  stm32f4xx_hal.o(.text.HAL_IncTick)
    HAL_Init                                 0x08001e29   Thumb Code    54  stm32f4xx_hal.o(.text.HAL_Init)
    HAL_InitTick                             0x08001e61   Thumb Code    72  stm32f4xx_hal.o(.text.HAL_InitTick)
    HAL_MspInit                              0x08001ea9   Thumb Code    56  stm32f4xx_hal_msp.o(.text.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08001ee1   Thumb Code    34  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08001f05   Thumb Code    86  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08001f5d   Thumb Code    32  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08001f7d   Thumb Code   356  stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x080020e1   Thumb Code    38  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08002109   Thumb Code    38  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08002131   Thumb Code   106  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x0800219d   Thumb Code   940  stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x08002549   Thumb Code    44  stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    HAL_UARTEx_ReceiveToIdle_DMA             0x08002575   Thumb Code   494  stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA)
    HAL_UARTEx_RxEventCallback               0x08002765   Thumb Code    52  usart.o(.text.HAL_UARTEx_RxEventCallback)
    HAL_UART_ErrorCallback                   0x08002799   Thumb Code     2  stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x0800279d   Thumb Code  1428  stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08002d31   Thumb Code    96  stm32f4xx_hal_uart.o(.text.HAL_UART_Init)
    HAL_UART_MspInit                         0x08002d91   Thumb Code   506  usart.o(.text.HAL_UART_MspInit)
    HAL_UART_RxCpltCallback                  0x08002f8d   Thumb Code     2  stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x08002f91   Thumb Code     2  stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x08002f95   Thumb Code   402  stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08003129   Thumb Code     2  stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x0800312d   Thumb Code     2  stm32f4xx_it.o(.text.HardFault_Handler)
    MX_DMA_Init                              0x08003131   Thumb Code   108  dma.o(.text.MX_DMA_Init)
    MX_GPIO_Init                             0x0800319d   Thumb Code    56  gpio.o(.text.MX_GPIO_Init)
    MX_UART4_Init                            0x080031d5   Thumb Code    60  usart.o(.text.MX_UART4_Init)
    MX_USART1_UART_Init                      0x08003211   Thumb Code    96  usart.o(.text.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x08003271   Thumb Code    60  usart.o(.text.MX_USART2_UART_Init)
    MemManage_Handler                        0x080032ad   Thumb Code     2  stm32f4xx_it.o(.text.MemManage_Handler)
    NMI_Handler                              0x080032b1   Thumb Code     2  stm32f4xx_it.o(.text.NMI_Handler)
    PendSV_Handler                           0x080032b5   Thumb Code     2  stm32f4xx_it.o(.text.PendSV_Handler)
    SVC_Handler                              0x080032b9   Thumb Code     2  stm32f4xx_it.o(.text.SVC_Handler)
    SysTick_Handler                          0x080032bd   Thumb Code     4  stm32f4xx_it.o(.text.SysTick_Handler)
    SystemClock_Config                       0x080032c1   Thumb Code   168  main.o(.text.SystemClock_Config)
    SystemInit                               0x08003369   Thumb Code    18  system_stm32f4xx.o(.text.SystemInit)
    UART4_IRQHandler                         0x0800337d   Thumb Code    12  stm32f4xx_it.o(.text.UART4_IRQHandler)
    USART1_IRQHandler                        0x08003871   Thumb Code    52  stm32f4xx_it.o(.text.USART1_IRQHandler)
    USART2_IRQHandler                        0x080038a5   Thumb Code    12  stm32f4xx_it.o(.text.USART2_IRQHandler)
    UsageFault_Handler                       0x080038b1   Thumb Code     2  stm32f4xx_it.o(.text.UsageFault_Handler)
    main                                     0x080038b5   Thumb Code    58  main.o(.text.main)
    my_printf                                0x080038f1   Thumb Code    62  uart.o(.text.my_printf)
    rt_ringbuffer_data_len                   0x08003931   Thumb Code    68  ringbuffer.o(.text.rt_ringbuffer_data_len)
    rt_ringbuffer_get                        0x08003975   Thumb Code   210  ringbuffer.o(.text.rt_ringbuffer_get)
    rt_ringbuffer_init                       0x08003a49   Thumb Code    56  ringbuffer.o(.text.rt_ringbuffer_init)
    rt_ringbuffer_put                        0x08003a99   Thumb Code   206  ringbuffer.o(.text.rt_ringbuffer_put)
    uart1_proc                               0x08003b69   Thumb Code    68  uart.o(.text.uart1_proc)
    _btod_d2e                                0x08003bb9   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08003bf7   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08003c3d   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08003c9d   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x08003fd5   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x080040b1   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x080040db   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x08004105   Thumb Code   580  btod.o(CL$$btod_mult_common)
    __ARM_fpclassify                         0x08004349   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    _is_digit                                0x08004379   Thumb Code    14  __printf_wp.o(i._is_digit)
    _get_lc_numeric                          0x08004389   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x080043b5   Thumb Code    44  lc_ctype_c.o(locale$$code)
    _fp_init                                 0x080043e1   Thumb Code    26  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x080043f9   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x080043f9   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    _printf_fp_dec                           0x080043fb   Thumb Code     4  printf1.o(x$fpl$printf1)
    _printf_fp_hex                           0x080043ff   Thumb Code     4  printf2.o(x$fpl$printf2)
    __I$use$fp                               0x08004402   Number         0  usenofp.o(x$fpl$usenofp)
    AHBPrescTable                            0x08004500   Data          16  system_stm32f4xx.o(.rodata.AHBPrescTable)
    APBPrescTable                            0x08004510   Data           8  system_stm32f4xx.o(.rodata.APBPrescTable)
    Region$$Table$$Base                      0x08004544   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08004564   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x0800458d   Data           0  lc_ctype_c.o(locale$$data)
    uwTickFreq                               0x20000000   Data           1  stm32f4xx_hal.o(.data..L_MergedGlobals)
    uwTickPrio                               0x20000004   Data           4  stm32f4xx_hal.o(.data..L_MergedGlobals)
    SystemCoreClock                          0x20000008   Data           4  system_stm32f4xx.o(.data.SystemCoreClock)
    __libspace_start                         0x20000010   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000070   Data           0  libspace.o(.bss)
    uart1_ring                               0x20000070   Data          12  uart.o(.bss..L_MergedGlobals)
    uart1_out                                0x2000007c   Data          64  uart.o(.bss..L_MergedGlobals)
    hdma_uart4_rx                            0x200000bc   Data          96  usart.o(.bss.hdma_uart4_rx)
    hdma_usart1_rx                           0x2000011c   Data          96  usart.o(.bss.hdma_usart1_rx)
    hdma_usart2_rx                           0x2000017c   Data          96  usart.o(.bss.hdma_usart2_rx)
    huart1                                   0x200001dc   Data          72  usart.o(.bss.huart1)
    huart2                                   0x20000224   Data          72  usart.o(.bss.huart2)
    huart4                                   0x2000026c   Data          72  usart.o(.bss.huart4)
    ringbuffer_pool_1                        0x200002b4   Data          64  usart.o(.bss.ringbuffer_pool_1)
    uart_1_buf                               0x200002f4   Data          64  usart.o(.bss.uart_1_buf)
    uwTick                                   0x20000334   Data           4  stm32f4xx_hal.o(.bss.uwTick)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x000046a0, Max: 0x00080000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00004690, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000008   Code   RO          692  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x0000005c   Code   RO         1049    !!!scatter          c_w.l(__scatter.o)
    0x080001ec   0x080001ec   0x0000001a   Code   RO         1053    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000206   0x08000206   0x00000002   PAD
    0x08000208   0x08000208   0x00000002   Code   RO         1050    !!handler_null      c_w.l(__scatter.o)
    0x0800020a   0x0800020a   0x00000002   PAD
    0x0800020c   0x0800020c   0x0000001c   Code   RO         1055    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000228   0x08000228   0x00000000   Code   RO          780    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x08000228   0x08000228   0x00000006   Code   RO          769    .ARM.Collect$$_printf_percent$$00000001  c_w.l(_printf_n.o)
    0x0800022e   0x0800022e   0x00000006   Code   RO          771    .ARM.Collect$$_printf_percent$$00000002  c_w.l(_printf_p.o)
    0x08000234   0x08000234   0x00000006   Code   RO          776    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x0800023a   0x0800023a   0x00000006   Code   RO          777    .ARM.Collect$$_printf_percent$$00000004  c_w.l(_printf_e.o)
    0x08000240   0x08000240   0x00000006   Code   RO          778    .ARM.Collect$$_printf_percent$$00000005  c_w.l(_printf_g.o)
    0x08000246   0x08000246   0x00000006   Code   RO          779    .ARM.Collect$$_printf_percent$$00000006  c_w.l(_printf_a.o)
    0x0800024c   0x0800024c   0x0000000a   Code   RO          784    .ARM.Collect$$_printf_percent$$00000007  c_w.l(_printf_ll.o)
    0x08000256   0x08000256   0x00000006   Code   RO          773    .ARM.Collect$$_printf_percent$$00000008  c_w.l(_printf_i.o)
    0x0800025c   0x0800025c   0x00000006   Code   RO          774    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000262   0x08000262   0x00000006   Code   RO          775    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x08000268   0x08000268   0x00000006   Code   RO          772    .ARM.Collect$$_printf_percent$$0000000B  c_w.l(_printf_o.o)
    0x0800026e   0x0800026e   0x00000006   Code   RO          770    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x08000274   0x08000274   0x00000006   Code   RO          781    .ARM.Collect$$_printf_percent$$0000000D  c_w.l(_printf_lli.o)
    0x0800027a   0x0800027a   0x00000006   Code   RO          782    .ARM.Collect$$_printf_percent$$0000000E  c_w.l(_printf_lld.o)
    0x08000280   0x08000280   0x00000006   Code   RO          783    .ARM.Collect$$_printf_percent$$0000000F  c_w.l(_printf_llu.o)
    0x08000286   0x08000286   0x00000006   Code   RO          788    .ARM.Collect$$_printf_percent$$00000010  c_w.l(_printf_llo.o)
    0x0800028c   0x0800028c   0x00000006   Code   RO          789    .ARM.Collect$$_printf_percent$$00000011  c_w.l(_printf_llx.o)
    0x08000292   0x08000292   0x0000000a   Code   RO          785    .ARM.Collect$$_printf_percent$$00000012  c_w.l(_printf_l.o)
    0x0800029c   0x0800029c   0x00000006   Code   RO          767    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x080002a2   0x080002a2   0x00000006   Code   RO          768    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x080002a8   0x080002a8   0x00000006   Code   RO          786    .ARM.Collect$$_printf_percent$$00000015  c_w.l(_printf_lc.o)
    0x080002ae   0x080002ae   0x00000006   Code   RO          787    .ARM.Collect$$_printf_percent$$00000016  c_w.l(_printf_ls.o)
    0x080002b4   0x080002b4   0x00000004   Code   RO          846    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x080002b8   0x080002b8   0x00000002   Code   RO          914    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080002ba   0x080002ba   0x00000004   Code   RO          938    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x080002be   0x080002be   0x00000000   Code   RO          941    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080002be   0x080002be   0x00000000   Code   RO          943    .ARM.Collect$$libinit$$00000006  c_w.l(libinit2.o)
    0x080002be   0x080002be   0x00000000   Code   RO          946    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080002be   0x080002be   0x00000000   Code   RO          948    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080002be   0x080002be   0x00000000   Code   RO          950    .ARM.Collect$$libinit$$00000010  c_w.l(libinit2.o)
    0x080002be   0x080002be   0x00000006   Code   RO          951    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080002c4   0x080002c4   0x00000000   Code   RO          953    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080002c4   0x080002c4   0x0000000c   Code   RO          954    .ARM.Collect$$libinit$$00000014  c_w.l(libinit2.o)
    0x080002d0   0x080002d0   0x00000000   Code   RO          955    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080002d0   0x080002d0   0x00000000   Code   RO          957    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080002d0   0x080002d0   0x0000000a   Code   RO          958    .ARM.Collect$$libinit$$00000018  c_w.l(libinit2.o)
    0x080002da   0x080002da   0x00000000   Code   RO          959    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080002da   0x080002da   0x00000000   Code   RO          961    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080002da   0x080002da   0x00000000   Code   RO          963    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080002da   0x080002da   0x00000000   Code   RO          965    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080002da   0x080002da   0x00000000   Code   RO          967    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080002da   0x080002da   0x00000000   Code   RO          969    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080002da   0x080002da   0x00000000   Code   RO          971    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080002da   0x080002da   0x00000000   Code   RO          973    .ARM.Collect$$libinit$$00000027  c_w.l(libinit2.o)
    0x080002da   0x080002da   0x00000000   Code   RO          977    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080002da   0x080002da   0x00000000   Code   RO          979    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080002da   0x080002da   0x00000000   Code   RO          981    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080002da   0x080002da   0x00000000   Code   RO          983    .ARM.Collect$$libinit$$00000034  c_w.l(libinit2.o)
    0x080002da   0x080002da   0x00000002   Code   RO          984    .ARM.Collect$$libinit$$00000035  c_w.l(libinit2.o)
    0x080002dc   0x080002dc   0x00000002   Code   RO         1020    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080002de   0x080002de   0x00000000   Code   RO         1032    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080002de   0x080002de   0x00000000   Code   RO         1034    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080002de   0x080002de   0x00000000   Code   RO         1037    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x080002de   0x080002de   0x00000000   Code   RO         1040    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x080002de   0x080002de   0x00000000   Code   RO         1042    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080002de   0x080002de   0x00000000   Code   RO         1045    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x080002de   0x080002de   0x00000002   Code   RO         1046    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x080002e0   0x080002e0   0x00000000   Code   RO          694    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080002e0   0x080002e0   0x00000000   Code   RO          813    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080002e0   0x080002e0   0x00000006   Code   RO          825    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080002e6   0x080002e6   0x00000000   Code   RO          815    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080002e6   0x080002e6   0x00000004   Code   RO          816    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080002ea   0x080002ea   0x00000000   Code   RO          818    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080002ea   0x080002ea   0x00000008   Code   RO          819    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080002f2   0x080002f2   0x00000002   Code   RO          923    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080002f4   0x080002f4   0x00000000   Code   RO          986    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080002f4   0x080002f4   0x00000004   Code   RO          987    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080002f8   0x080002f8   0x00000006   Code   RO          988    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080002fe   0x080002fe   0x00000002   PAD
    0x08000300   0x08000300   0x00000040   Code   RO            4    .text               startup_stm32f407xx.o
    0x08000340   0x08000340   0x000000f0   Code   RO          680    .text               c_w.l(lludivv7m.o)
    0x08000430   0x08000430   0x0000003c   Code   RO          682    .text               c_w.l(vsnprintf.o)
    0x0800046c   0x0800046c   0x00000080   Code   RO          684    .text               c_w.l(assert.o)
    0x080004ec   0x080004ec   0x0000008a   Code   RO          686    .text               c_w.l(rt_memcpy_v6.o)
    0x08000576   0x08000576   0x0000004e   Code   RO          688    .text               c_w.l(rt_memclr_w.o)
    0x080005c4   0x080005c4   0x00000006   Code   RO          690    .text               c_w.l(heapauxi.o)
    0x080005ca   0x080005ca   0x0000004e   Code   RO          701    .text               c_w.l(_printf_pad.o)
    0x08000618   0x08000618   0x00000024   Code   RO          703    .text               c_w.l(_printf_truncate.o)
    0x0800063c   0x0800063c   0x00000052   Code   RO          705    .text               c_w.l(_printf_str.o)
    0x0800068e   0x0800068e   0x00000002   PAD
    0x08000690   0x08000690   0x00000078   Code   RO          707    .text               c_w.l(_printf_dec.o)
    0x08000708   0x08000708   0x00000028   Code   RO          709    .text               c_w.l(_printf_charcount.o)
    0x08000730   0x08000730   0x00000030   Code   RO          711    .text               c_w.l(_printf_char_common.o)
    0x08000760   0x08000760   0x00000010   Code   RO          713    .text               c_w.l(_snputc.o)
    0x08000770   0x08000770   0x000000bc   Code   RO          715    .text               c_w.l(_printf_wctomb.o)
    0x0800082c   0x0800082c   0x0000007c   Code   RO          718    .text               c_w.l(_printf_longlong_dec.o)
    0x080008a8   0x080008a8   0x00000070   Code   RO          724    .text               c_w.l(_printf_oct_int_ll.o)
    0x08000918   0x08000918   0x00000094   Code   RO          744    .text               c_w.l(_printf_hex_int_ll_ptr.o)
    0x080009ac   0x080009ac   0x00000188   Code   RO          764    .text               c_w.l(__printf_flags_ss_wp.o)
    0x08000b34   0x08000b34   0x00000016   Code   RO          790    .text               c_w.l(abort.o)
    0x08000b4a   0x08000b4a   0x00000014   Code   RO          792    .text               c_w.l(assert_puts.o)
    0x08000b5e   0x08000b5e   0x00000064   Code   RO          794    .text               c_w.l(rt_memcpy_w.o)
    0x08000bc2   0x08000bc2   0x0000000e   Code   RO          804    .text               c_w.l(sys_wrch.o)
    0x08000bd0   0x08000bd0   0x0000000c   Code   RO          806    .text               c_w.l(sys_exit.o)
    0x08000bdc   0x08000bdc   0x0000008a   Code   RO          829    .text               c_w.l(lludiv10.o)
    0x08000c66   0x08000c66   0x000000b2   Code   RO          831    .text               c_w.l(_printf_intcommon.o)
    0x08000d18   0x08000d18   0x0000041c   Code   RO          833    .text               c_w.l(_printf_fp_dec.o)
    0x08001134   0x08001134   0x000002fc   Code   RO          837    .text               c_w.l(_printf_fp_hex.o)
    0x08001430   0x08001430   0x0000002c   Code   RO          842    .text               c_w.l(_printf_char.o)
    0x0800145c   0x0800145c   0x0000002c   Code   RO          844    .text               c_w.l(_printf_wchar.o)
    0x08001488   0x08001488   0x00000048   Code   RO          847    .text               c_w.l(_c16rtomb.o)
    0x080014d0   0x080014d0   0x0000000e   Code   RO          849    .text               c_w.l(defsig_abrt_outer.o)
    0x080014de   0x080014de   0x00000002   Code   RO          853    .text               c_w.l(use_no_semi.o)
    0x080014e0   0x080014e0   0x00000000   Code   RO          855    .text               c_w.l(indicate_semi.o)
    0x080014e0   0x080014e0   0x0000004a   Code   RO          856    .text               c_w.l(sys_stackheap_outer.o)
    0x0800152a   0x0800152a   0x00000002   PAD
    0x0800152c   0x0800152c   0x00000010   Code   RO          858    .text               c_w.l(rt_ctype_table.o)
    0x0800153c   0x0800153c   0x00000008   Code   RO          865    .text               c_w.l(rt_locale_intlibspace.o)
    0x08001544   0x08001544   0x00000080   Code   RO          867    .text               c_w.l(_printf_fp_infnan.o)
    0x080015c4   0x080015c4   0x000000e4   Code   RO          869    .text               c_w.l(bigflt0.o)
    0x080016a8   0x080016a8   0x00000012   Code   RO          899    .text               c_w.l(exit.o)
    0x080016ba   0x080016ba   0x0000000a   Code   RO          901    .text               c_w.l(defsig_exit.o)
    0x080016c4   0x080016c4   0x00000030   Code   RO          903    .text               c_w.l(defsig_abrt_inner.o)
    0x080016f4   0x080016f4   0x00000008   Code   RO          920    .text               c_w.l(libspace.o)
    0x080016fc   0x080016fc   0x00000032   Code   RO          932    .text               c_w.l(defsig_general.o)
    0x0800172e   0x0800172e   0x00000002   PAD
    0x08001730   0x08001730   0x0000007c   Code   RO          936    .text               c_w.l(strcmpv7em.o)
    0x080017ac   0x080017ac   0x00000002   Code   RO           76    .text.BusFault_Handler  stm32f4xx_it.o
    0x080017ae   0x080017ae   0x00000002   PAD
    0x080017b0   0x080017b0   0x0000000c   Code   RO           88    .text.DMA1_Stream2_IRQHandler  stm32f4xx_it.o
    0x080017bc   0x080017bc   0x0000000c   Code   RO           90    .text.DMA1_Stream5_IRQHandler  stm32f4xx_it.o
    0x080017c8   0x080017c8   0x0000000c   Code   RO           98    .text.DMA2_Stream2_IRQHandler  stm32f4xx_it.o
    0x080017d4   0x080017d4   0x00000002   Code   RO           82    .text.DebugMon_Handler  stm32f4xx_it.o
    0x080017d6   0x080017d6   0x00000002   PAD
    0x080017d8   0x080017d8   0x00000006   Code   RO           15    .text.Error_Handler  main.o
    0x080017de   0x080017de   0x00000002   PAD
    0x080017e0   0x080017e0   0x0000008e   Code   RO          393    .text.HAL_DMA_Abort  stm32f4xx_hal_dma.o
    0x0800186e   0x0800186e   0x00000002   PAD
    0x08001870   0x08001870   0x00000024   Code   RO          395    .text.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x08001894   0x08001894   0x000001c4   Code   RO          399    .text.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x08001a58   0x08001a58   0x00000162   Code   RO          385    .text.HAL_DMA_Init  stm32f4xx_hal_dma.o
    0x08001bba   0x08001bba   0x00000002   PAD
    0x08001bbc   0x08001bbc   0x000000a2   Code   RO          391    .text.HAL_DMA_Start_IT  stm32f4xx_hal_dma.o
    0x08001c5e   0x08001c5e   0x00000002   PAD
    0x08001c60   0x08001c60   0x0000019e   Code   RO          347    .text.HAL_GPIO_Init  stm32f4xx_hal_gpio.o
    0x08001dfe   0x08001dfe   0x00000002   PAD
    0x08001e00   0x08001e00   0x0000000c   Code   RO          544    .text.HAL_GetTick   stm32f4xx_hal.o
    0x08001e0c   0x08001e0c   0x0000001a   Code   RO          542    .text.HAL_IncTick   stm32f4xx_hal.o
    0x08001e26   0x08001e26   0x00000002   PAD
    0x08001e28   0x08001e28   0x00000036   Code   RO          532    .text.HAL_Init      stm32f4xx_hal.o
    0x08001e5e   0x08001e5e   0x00000002   PAD
    0x08001e60   0x08001e60   0x00000048   Code   RO          534    .text.HAL_InitTick  stm32f4xx_hal.o
    0x08001ea8   0x08001ea8   0x00000038   Code   RO          107    .text.HAL_MspInit   stm32f4xx_hal_msp.o
    0x08001ee0   0x08001ee0   0x00000022   Code   RO          484    .text.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x08001f02   0x08001f02   0x00000002   PAD
    0x08001f04   0x08001f04   0x00000056   Code   RO          482    .text.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08001f5a   0x08001f5a   0x00000002   PAD
    0x08001f5c   0x08001f5c   0x00000020   Code   RO          480    .text.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08001f7c   0x08001f7c   0x00000164   Code   RO          241    .text.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x080020e0   0x080020e0   0x00000026   Code   RO          253    .text.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x08002106   0x08002106   0x00000002   PAD
    0x08002108   0x08002108   0x00000026   Code   RO          255    .text.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x0800212e   0x0800212e   0x00000002   PAD
    0x08002130   0x08002130   0x0000006a   Code   RO          243    .text.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x0800219a   0x0800219a   0x00000002   PAD
    0x0800219c   0x0800219c   0x000003ac   Code   RO          239    .text.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x08002548   0x08002548   0x0000002c   Code   RO          492    .text.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x08002574   0x08002574   0x000001ee   Code   RO          163    .text.HAL_UARTEx_ReceiveToIdle_DMA  stm32f4xx_hal_uart.o
    0x08002762   0x08002762   0x00000002   PAD
    0x08002764   0x08002764   0x00000034   Code   RO           42    .text.HAL_UARTEx_RxEventCallback  usart.o
    0x08002798   0x08002798   0x00000002   Code   RO          199    .text.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x0800279a   0x0800279a   0x00000002   PAD
    0x0800279c   0x0800279c   0x00000594   Code   RO          193    .text.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x08002d30   0x08002d30   0x00000060   Code   RO          115    .text.HAL_UART_Init  stm32f4xx_hal_uart.o
    0x08002d90   0x08002d90   0x000001fa   Code   RO           50    .text.HAL_UART_MspInit  usart.o
    0x08002f8a   0x08002f8a   0x00000002   PAD
    0x08002f8c   0x08002f8c   0x00000002   Code   RO          207    .text.HAL_UART_RxCpltCallback  stm32f4xx_hal_uart.o
    0x08002f8e   0x08002f8e   0x00000002   PAD
    0x08002f90   0x08002f90   0x00000002   Code   RO          209    .text.HAL_UART_RxHalfCpltCallback  stm32f4xx_hal_uart.o
    0x08002f92   0x08002f92   0x00000002   PAD
    0x08002f94   0x08002f94   0x00000192   Code   RO          131    .text.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x08003126   0x08003126   0x00000002   PAD
    0x08003128   0x08003128   0x00000002   Code   RO          203    .text.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x0800312a   0x0800312a   0x00000002   PAD
    0x0800312c   0x0800312c   0x00000002   Code   RO           72    .text.HardFault_Handler  stm32f4xx_it.o
    0x0800312e   0x0800312e   0x00000002   PAD
    0x08003130   0x08003130   0x0000006c   Code   RO           33    .text.MX_DMA_Init   dma.o
    0x0800319c   0x0800319c   0x00000038   Code   RO           25    .text.MX_GPIO_Init  gpio.o
    0x080031d4   0x080031d4   0x0000003c   Code   RO           44    .text.MX_UART4_Init  usart.o
    0x08003210   0x08003210   0x00000060   Code   RO           46    .text.MX_USART1_UART_Init  usart.o
    0x08003270   0x08003270   0x0000003c   Code   RO           48    .text.MX_USART2_UART_Init  usart.o
    0x080032ac   0x080032ac   0x00000002   Code   RO           74    .text.MemManage_Handler  stm32f4xx_it.o
    0x080032ae   0x080032ae   0x00000002   PAD
    0x080032b0   0x080032b0   0x00000002   Code   RO           70    .text.NMI_Handler   stm32f4xx_it.o
    0x080032b2   0x080032b2   0x00000002   PAD
    0x080032b4   0x080032b4   0x00000002   Code   RO           84    .text.PendSV_Handler  stm32f4xx_it.o
    0x080032b6   0x080032b6   0x00000002   PAD
    0x080032b8   0x080032b8   0x00000002   Code   RO           80    .text.SVC_Handler   stm32f4xx_it.o
    0x080032ba   0x080032ba   0x00000002   PAD
    0x080032bc   0x080032bc   0x00000004   Code   RO           86    .text.SysTick_Handler  stm32f4xx_it.o
    0x080032c0   0x080032c0   0x000000a8   Code   RO           13    .text.SystemClock_Config  main.o
    0x08003368   0x08003368   0x00000012   Code   RO          622    .text.SystemInit    system_stm32f4xx.o
    0x0800337a   0x0800337a   0x00000002   PAD
    0x0800337c   0x0800337c   0x0000000c   Code   RO           96    .text.UART4_IRQHandler  stm32f4xx_it.o
    0x08003388   0x08003388   0x0000000a   Code   RO          197    .text.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x08003392   0x08003392   0x00000002   PAD
    0x08003394   0x08003394   0x0000017c   Code   RO          147    .text.UART_DMAError  stm32f4xx_hal_uart.o
    0x08003510   0x08003510   0x0000015e   Code   RO          225    .text.UART_DMAReceiveCplt  stm32f4xx_hal_uart.o
    0x0800366e   0x0800366e   0x00000002   PAD
    0x08003670   0x08003670   0x00000018   Code   RO          227    .text.UART_DMARxHalfCplt  stm32f4xx_hal_uart.o
    0x08003688   0x08003688   0x000000fe   Code   RO          195    .text.UART_Receive_IT  stm32f4xx_hal_uart.o
    0x08003786   0x08003786   0x00000002   PAD
    0x08003788   0x08003788   0x000000e6   Code   RO          119    .text.UART_SetConfig  stm32f4xx_hal_uart.o
    0x0800386e   0x0800386e   0x00000002   PAD
    0x08003870   0x08003870   0x00000034   Code   RO           92    .text.USART1_IRQHandler  stm32f4xx_it.o
    0x080038a4   0x080038a4   0x0000000c   Code   RO           94    .text.USART2_IRQHandler  stm32f4xx_it.o
    0x080038b0   0x080038b0   0x00000002   Code   RO           78    .text.UsageFault_Handler  stm32f4xx_it.o
    0x080038b2   0x080038b2   0x00000002   PAD
    0x080038b4   0x080038b4   0x0000003a   Code   RO           11    .text.main          main.o
    0x080038ee   0x080038ee   0x00000002   PAD
    0x080038f0   0x080038f0   0x0000003e   Code   RO          668    .text.my_printf     uart.o
    0x0800392e   0x0800392e   0x00000002   PAD
    0x08003930   0x08003930   0x00000044   Code   RO          641    .text.rt_ringbuffer_data_len  ringbuffer.o
    0x08003974   0x08003974   0x000000d2   Code   RO          645    .text.rt_ringbuffer_get  ringbuffer.o
    0x08003a46   0x08003a46   0x00000002   PAD
    0x08003a48   0x08003a48   0x00000050   Code   RO          637    .text.rt_ringbuffer_init  ringbuffer.o
    0x08003a98   0x08003a98   0x000000ce   Code   RO          639    .text.rt_ringbuffer_put  ringbuffer.o
    0x08003b66   0x08003b66   0x00000002   PAD
    0x08003b68   0x08003b68   0x00000050   Code   RO          666    .text.uart1_proc    uart.o
    0x08003bb8   0x08003bb8   0x0000003e   Code   RO          872    CL$$btod_d2e        c_w.l(btod.o)
    0x08003bf6   0x08003bf6   0x00000046   Code   RO          874    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08003c3c   0x08003c3c   0x00000060   Code   RO          873    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08003c9c   0x08003c9c   0x00000338   Code   RO          882    CL$$btod_div_common  c_w.l(btod.o)
    0x08003fd4   0x08003fd4   0x000000dc   Code   RO          879    CL$$btod_e2e        c_w.l(btod.o)
    0x080040b0   0x080040b0   0x0000002a   Code   RO          876    CL$$btod_ediv       c_w.l(btod.o)
    0x080040da   0x080040da   0x0000002a   Code   RO          875    CL$$btod_emul       c_w.l(btod.o)
    0x08004104   0x08004104   0x00000244   Code   RO          881    CL$$btod_mult_common  c_w.l(btod.o)
    0x08004348   0x08004348   0x00000030   Code   RO          918    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x08004378   0x08004378   0x0000000e   Code   RO          757    i._is_digit         c_w.l(__printf_wp.o)
    0x08004386   0x08004386   0x00000002   PAD
    0x08004388   0x08004388   0x0000002c   Code   RO          897    locale$$code        c_w.l(lc_numeric_c.o)
    0x080043b4   0x080043b4   0x0000002c   Code   RO          928    locale$$code        c_w.l(lc_ctype_c.o)
    0x080043e0   0x080043e0   0x0000001a   Code   RO          998    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x080043fa   0x080043fa   0x00000004   Code   RO          796    x$fpl$printf1       fz_wm.l(printf1.o)
    0x080043fe   0x080043fe   0x00000004   Code   RO          798    x$fpl$printf2       fz_wm.l(printf2.o)
    0x08004402   0x08004402   0x00000000   Code   RO          917    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x08004402   0x08004402   0x00000002   PAD
    0x08004404   0x08004404   0x00000008   Data   RO          716    .constdata          c_w.l(_printf_wctomb.o)
    0x0800440c   0x0800440c   0x00000028   Data   RO          745    .constdata          c_w.l(_printf_hex_int_ll_ptr.o)
    0x08004434   0x08004434   0x00000011   Data   RO          765    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x08004445   0x08004445   0x00000026   Data   RO          838    .constdata          c_w.l(_printf_fp_hex.o)
    0x0800446b   0x0800446b   0x00000001   PAD
    0x0800446c   0x0800446c   0x00000094   Data   RO          870    .constdata          c_w.l(bigflt0.o)
    0x08004500   0x08004500   0x00000010   Data   RO          627    .rodata.AHBPrescTable  system_stm32f4xx.o
    0x08004510   0x08004510   0x00000008   Data   RO          628    .rodata.APBPrescTable  system_stm32f4xx.o
    0x08004518   0x08004518   0x00000008   Data   RO          409    .rodata.cst8        stm32f4xx_hal_dma.o
    0x08004520   0x08004520   0x00000022   Data   RO          657    .rodata.str1.1      ringbuffer.o
    0x08004542   0x08004542   0x00000002   PAD
    0x08004544   0x08004544   0x00000020   Data   RO         1048    Region$$Table       anon$$obj.o
    0x08004564   0x08004564   0x0000001c   Data   RO          896    locale$$data        c_w.l(lc_numeric_c.o)
    0x08004580   0x08004580   0x00000110   Data   RO          927    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08004690, Size: 0x00000938, Max: 0x0001c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08004690   0x00000008   Data   RW          587    .data..L_MergedGlobals  stm32f4xx_hal.o
    0x20000008   0x08004698   0x00000004   Data   RW          626    .data.SystemCoreClock  system_stm32f4xx.o
    0x2000000c   0x0800469c   0x00000004   PAD
    0x20000010        -       0x00000060   Zero   RW          921    .bss                c_w.l(libspace.o)
    0x20000070        -       0x0000004c   Zero   RW          670    .bss..L_MergedGlobals  uart.o
    0x200000bc        -       0x00000060   Zero   RW           59    .bss.hdma_uart4_rx  usart.o
    0x2000011c        -       0x00000060   Zero   RW           57    .bss.hdma_usart1_rx  usart.o
    0x2000017c        -       0x00000060   Zero   RW           60    .bss.hdma_usart2_rx  usart.o
    0x200001dc        -       0x00000048   Zero   RW           56    .bss.huart1         usart.o
    0x20000224        -       0x00000048   Zero   RW           58    .bss.huart2         usart.o
    0x2000026c        -       0x00000048   Zero   RW           55    .bss.huart4         usart.o
    0x200002b4        -       0x00000040   Zero   RW           61    .bss.ringbuffer_pool_1  usart.o
    0x200002f4        -       0x00000040   Zero   RW           54    .bss.uart_1_buf     usart.o
    0x20000334        -       0x00000004   Zero   RW          586    .bss.uwTick         stm32f4xx_hal.o
    0x20000338        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f407xx.o
    0x20000538        -       0x00000400   Zero   RW            1    STACK               startup_stm32f407xx.o


    Execution Region RW_IRAM2 (Exec base: 0x2001c000, Load base: 0x080046a0, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       108          0          0          0          0       3516   dma.o
        56          0          0          0          0       1442   gpio.o
       232          0          0          0          0       2915   main.o
       564         24         34          0          0       7041   ringbuffer.o
        64         26        392          0       1536        864   startup_stm32f407xx.o
       164          0          0          8          4       7312   stm32f4xx_hal.o
       196          0          0          0          0      10729   stm32f4xx_hal_cortex.o
      1146          6          8          0          0      10541   stm32f4xx_hal_dma.o
       414          0          0          0          0       5359   stm32f4xx_hal_gpio.o
        56          0          0          0          0       1467   stm32f4xx_hal_msp.o
      1478          0          0          0          0       7419   stm32f4xx_hal_rcc.o
      3676          0          0          0          0      32252   stm32f4xx_hal_uart.o
       132          0          0          0          0       4647   stm32f4xx_it.o
        18          0         24          4          0       2596   system_stm32f4xx.o
       142         12          0          0         76       4393   uart.o
       774          0          0          0        632       8024   usart.o

    ----------------------------------------------------------------------
      9292         <USER>        <GROUP>         12       2252     110517   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        72          0          2          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        94          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        72          0          0          0          0         96   _c16rtomb.o
         6          0          0          0          0          0   _printf_a.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        40          0          0          0          0         68   _printf_charcount.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_e.o
         6          0          0          0          0          0   _printf_f.o
      1052          0          0          0          0        148   _printf_fp_dec.o
       764          8         38          0          0        100   _printf_fp_hex.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         6          0          0          0          0          0   _printf_g.o
       148          4         40          0          0        160   _printf_hex_int_ll_ptr.o
         6          0          0          0          0          0   _printf_i.o
       178          0          0          0          0         88   _printf_intcommon.o
        10          0          0          0          0          0   _printf_l.o
         6          0          0          0          0          0   _printf_lc.o
        10          0          0          0          0          0   _printf_ll.o
         6          0          0          0          0          0   _printf_lld.o
         6          0          0          0          0          0   _printf_lli.o
         6          0          0          0          0          0   _printf_llo.o
         6          0          0          0          0          0   _printf_llu.o
         6          0          0          0          0          0   _printf_llx.o
       124         16          0          0          0         92   _printf_longlong_dec.o
         6          0          0          0          0          0   _printf_ls.o
         6          0          0          0          0          0   _printf_n.o
         6          0          0          0          0          0   _printf_o.o
       112          8          0          0          0        124   _printf_oct_int_ll.o
         6          0          0          0          0          0   _printf_p.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        36          0          0          0          0         84   _printf_truncate.o
         6          0          0          0          0          0   _printf_u.o
        44          0          0          0          0        108   _printf_wchar.o
       188          6          8          0          0         92   _printf_wctomb.o
         6          0          0          0          0          0   _printf_x.o
        16          0          0          0          0         68   _snputc.o
        22          0          0          0          0         80   abort.o
       128         42          0          0          0         76   assert.o
        20          0          0          0          0         76   assert_puts.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        668   btod.o
        48         34          0          0          0         76   defsig_abrt_inner.o
        14          0          0          0          0         80   defsig_abrt_outer.o
        10          0          0          0          0         68   defsig_exit.o
        50          0          0          0          0         88   defsig_general.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        34          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
       240          0          0          0          0        100   lludivv7m.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       124          0          0          0          0         88   strcmpv7em.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
        14          0          0          0          0         76   sys_wrch.o
         2          0          0          0          0         68   use_no_semi.o
        60          4          0          0          0         80   vsnprintf.o
        26          0          0          0          0        116   fpinit.o
         4          0          0          0          0        116   printf1.o
         4          0          0          0          0        116   printf2.o
         0          0          0          0          0          0   usenofp.o
        48          0          0          0          0        124   fpclassify.o

    ----------------------------------------------------------------------
      7728        <USER>        <GROUP>          0         96       5268   Library Totals
        16          0          1          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      7630        340        551          0         96       4796   c_w.l
        34          0          0          0          0        348   fz_wm.l
        48          0          0          0          0        124   m_wm.l

    ----------------------------------------------------------------------
      7728        <USER>        <GROUP>          0         96       5268   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     17020        408       1044         12       2348     112721   Grand Totals
     17020        408       1044         12       2348     112721   ELF Image Totals
     17020        408       1044         12          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                18064 (  17.64kB)
    Total RW  Size (RW Data + ZI Data)              2360 (   2.30kB)
    Total ROM Size (Code + RO Data + RW Data)      18076 (  17.65kB)

==============================================================================

