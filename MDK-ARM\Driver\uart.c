#include "uart.h"

struct rt_ringbuffer uart1_ring;
uint8_t uart1_out[64];

struct rt_ringbuffer uart2_ring;
struct rt_ringbuffer uart4_ring;


void uart1_proc()
{
	uint16_t length_1;

	length_1 = rt_ringbuffer_data_len(&uart1_ring);
//	my_printf(&huart1, "abc%d\n",length_x);
	if (length_1 > 0)
	{
		rt_ringbuffer_get(&uart1_ring, uart1_out, length_1);
		uart1_out[length_1] = '\0';
		my_printf(&huart1, "user:%s\n",uart1_out);

		memset(uart1_out, 0, length_1);
	}
}

void uart2_proc()
{
	uint16_t length_2;

	length_2 = rt_ringbuffer_data_len(&uart2_ring);
	if (length_2 > 0)
	{
		rt_ringbuffer_get(&uart2_ring, uart2_out, length_2);
		uart2_out[length_2] = '\0';
		my_printf(&huart1, "UART2:%s\n", uart2_out); // 回显到UART1

		memset(uart2_out, 0, length_2);
	}
}

void uart4_proc()
{
	uint16_t length_4;

	length_4 = rt_ringbuffer_data_len(&uart4_ring);
	if (length_4 > 0)
	{
		rt_ringbuffer_get(&uart4_ring, uart4_out, length_4);
		uart4_out[length_4] = '\0';
		my_printf(&huart1, "UART4:%s\n", uart4_out); // 回显到UART1

		memset(uart4_out, 0, length_4);
	}
}












int my_printf(UART_HandleTypeDef *huart, const char *format, ...)
{
	char buffer[512]; // ��ʱ�洢��ʽ������ַ���
	va_list arg;      // �����ɱ����
	int len;          // �����ַ�������

	va_start(arg, format);
	// ��ȫ�ظ�ʽ���ַ����� buffer
	len = vsnprintf(buffer, sizeof(buffer), format, arg);
	va_end(arg);

	// ͨ�� HAL �ⷢ�� buffer �е�����
	HAL_UART_Transmit(huart, (uint8_t *)buffer, (uint16_t)len, 0xFF);
	return len;
}